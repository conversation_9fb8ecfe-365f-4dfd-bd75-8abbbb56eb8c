import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from './context/AuthContext';

// Layout Components
import Layout from './components/Layout/Layout';
import AdminLayout from './components/Layout/AdminLayout';

// Public Pages
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import ArtistProfile from './pages/ArtistProfile';
import Search from './pages/Search';

// Protected Pages
import Dashboard from './pages/Dashboard';
import Profile from './pages/Profile';
import UploadSong from './pages/UploadSong';
import Analytics from './pages/Analytics';

// Components
import LoadingSpinner from './components/UI/LoadingSpinner';
import ProtectedRoute from './components/Auth/ProtectedRoute';

function App() {
  const { isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="App">
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="login" element={<Login />} />
          <Route path="register" element={<Register />} />
          <Route path="artist/:id" element={<ArtistProfile />} />
          <Route path="search" element={<Search />} />
        </Route>

        {/* Protected Admin/Artist Routes */}
        <Route path="/admin" element={
          <ProtectedRoute roles={['admin', 'artist']}>
            <AdminLayout />
          </ProtectedRoute>
        }>
          <Route index element={<Dashboard />} />
          <Route path="profile" element={<Profile />} />
          <Route path="upload" element={
            <ProtectedRoute roles={['admin', 'artist']}>
              <UploadSong />
            </ProtectedRoute>
          } />
          <Route path="analytics" element={
            <ProtectedRoute roles={['admin', 'artist']}>
              <Analytics />
            </ProtectedRoute>
          } />
        </Route>

        {/* Catch all route */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </div>
  );
}

export default App;
