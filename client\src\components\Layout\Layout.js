import React from 'react';
import { Outlet } from 'react-router-dom';
import Header from './Header';
import MusicPlayer from '../Player/MusicPlayer';
import { usePlayer } from '../../context/PlayerContext';

const Layout = () => {
  const { currentSong } = usePlayer();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      <Header />
      
      <main className={`flex-1 ${currentSong ? 'pb-24' : 'pb-4'}`}>
        <Outlet />
      </main>
      
      {currentSong && (
        <div className="fixed bottom-0 left-0 right-0 z-50">
          <MusicPlayer />
        </div>
      )}
    </div>
  );
};

export default Layout;
