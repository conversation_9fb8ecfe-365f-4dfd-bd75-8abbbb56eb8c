import React from 'react';
import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';
import { Play, TrendingUp, Star, Users } from 'lucide-react';
import { songsAPI, artistsAPI } from '../utils/api';
import { usePlayer } from '../context/PlayerContext';
import LoadingSpinner from '../components/UI/LoadingSpinner';

const Home = () => {
  const { playSong } = usePlayer();

  // Fetch featured songs
  const { data: featuredSongs, isLoading: songsLoading } = useQuery(
    'featured-songs',
    () => songsAPI.getAll({ featured: true, limit: 6 }),
    {
      select: (response) => response.data.songs,
    }
  );

  // Fetch trending songs
  const { data: trendingSongs, isLoading: trendingLoading } = useQuery(
    'trending-songs',
    () => songsAPI.getAll({ sort: 'trending', limit: 8 }),
    {
      select: (response) => response.data.songs,
    }
  );

  // Fetch popular artists
  const { data: popularArtists, isLoading: artistsLoading } = useQuery(
    'popular-artists',
    () => artistsAPI.getAll({ sort: 'popular', limit: 6 }),
    {
      select: (response) => response.data.artists,
    }
  );

  const handlePlaySong = (song, songs) => {
    const songIndex = songs.findIndex(s => s.id === song.id);
    playSong(song, songs, songIndex);
  };

  if (songsLoading || trendingLoading || artistsLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              We Down Stars
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Discover amazing music from talented artists around the world
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/register"
                className="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors"
              >
                Start Listening
              </Link>
              <Link
                to="/register?role=artist"
                className="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors"
              >
                Join as Artist
              </Link>
            </div>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Featured Songs */}
        <section className="mb-16">
          <div className="flex items-center mb-8">
            <Star className="h-6 w-6 text-yellow-500 mr-2" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Featured Songs
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredSongs?.map((song) => (
              <div
                key={song.id}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow p-4"
              >
                <div className="relative group">
                  <img
                    src={song.coverArt || '/default-cover.jpg'}
                    alt={song.title}
                    className="w-full h-48 object-cover rounded-md"
                  />
                  <button
                    onClick={() => handlePlaySong(song, featuredSongs)}
                    className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-md"
                  >
                    <Play className="h-12 w-12 text-white" fill="white" />
                  </button>
                </div>
                <div className="mt-4">
                  <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                    {song.title}
                  </h3>
                  <Link
                    to={`/artist/${song.artist.id}`}
                    className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 truncate block"
                  >
                    {song.artist.stageName}
                  </Link>
                  <div className="flex items-center mt-2 text-sm text-gray-500 dark:text-gray-400">
                    <Play className="h-4 w-4 mr-1" />
                    {song.playCount?.toLocaleString() || 0} plays
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Trending Songs */}
        <section className="mb-16">
          <div className="flex items-center mb-8">
            <TrendingUp className="h-6 w-6 text-green-500 mr-2" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Trending Now
            </h2>
          </div>
          
          <div className="space-y-4">
            {trendingSongs?.map((song, index) => (
              <div
                key={song.id}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 flex items-center space-x-4"
              >
                <div className="text-2xl font-bold text-gray-400 w-8">
                  {index + 1}
                </div>
                <img
                  src={song.coverArt || '/default-cover.jpg'}
                  alt={song.title}
                  className="w-16 h-16 object-cover rounded-md"
                />
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                    {song.title}
                  </h3>
                  <Link
                    to={`/artist/${song.artist.id}`}
                    className="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 truncate block"
                  >
                    {song.artist.stageName}
                  </Link>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {song.playCount?.toLocaleString() || 0} plays
                </div>
                <button
                  onClick={() => handlePlaySong(song, trendingSongs)}
                  className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                >
                  <Play className="h-5 w-5" />
                </button>
              </div>
            ))}
          </div>
        </section>

        {/* Popular Artists */}
        <section>
          <div className="flex items-center mb-8">
            <Users className="h-6 w-6 text-purple-500 mr-2" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Popular Artists
            </h2>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {popularArtists?.map((artist) => (
              <Link
                key={artist.id}
                to={`/artist/${artist.id}`}
                className="text-center group"
              >
                <img
                  src={artist.profileImage || '/default-avatar.jpg'}
                  alt={artist.stageName}
                  className="w-24 h-24 mx-auto rounded-full object-cover group-hover:scale-105 transition-transform"
                />
                <h3 className="mt-2 font-semibold text-gray-900 dark:text-white truncate">
                  {artist.stageName}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {artist.followerCount?.toLocaleString() || 0} followers
                </p>
              </Link>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
};

export default Home;
