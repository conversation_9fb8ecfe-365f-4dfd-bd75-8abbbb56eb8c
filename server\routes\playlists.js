const express = require('express');
const { body, validationResult } = require('express-validator');
const { Playlist, Song, Artist, User } = require('../models');
const { authenticateToken, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Get all public playlists
router.get('/', optionalAuth, async (req, res) => {
  try {
    const playlists = await Playlist.findAll({
      where: { isPublic: true },
      include: [{
        model: User,
        as: 'creator',
        attributes: ['id', 'username', 'firstName', 'lastName']
      }],
      order: [['createdAt', 'DESC']],
      limit: 20
    });

    res.json({ playlists });
  } catch (error) {
    console.error('Get playlists error:', error);
    res.status(500).json({ error: 'Failed to fetch playlists' });
  }
});

// Create playlist
router.post('/', authenticateToken, [
  body('name').notEmpty().withMessage('Playlist name is required'),
  body('description').optional().isString(),
  body('isPublic').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const playlist = await Playlist.create({
      ...req.body,
      userId: req.user.id
    });

    res.status(201).json({
      message: 'Playlist created successfully',
      playlist
    });
  } catch (error) {
    console.error('Create playlist error:', error);
    res.status(500).json({ error: 'Failed to create playlist' });
  }
});

module.exports = router;
