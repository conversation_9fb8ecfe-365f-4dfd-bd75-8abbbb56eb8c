const { User, Artist, Song, Playlist, Like, Comment } = require('../models');
require('dotenv').config();

const seedData = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    // Create sample users
    const users = await User.bulkCreate([
      {
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        isVerified: true
      },
      {
        username: 'theweeknd',
        email: '<EMAIL>',
        password: 'artist123',
        role: 'artist',
        firstName: '<PERSON>',
        lastName: 'Tesfaye',
        isVerified: true
      },
      {
        username: 'taylorswift',
        email: '<EMAIL>',
        password: 'artist123',
        role: 'artist',
        firstName: '<PERSON>',
        lastName: 'Swift',
        isVerified: true
      },
      {
        username: 'd<PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        password: 'user123',
        role: 'user',
        firstName: 'Music',
        lastName: 'Lover',
        isVerified: true
      }
    ], { individualHooks: true });

    console.log('✅ Users created');

    // Create sample artists
    const artists = await Artist.bulkCreate([
      {
        userId: users[1].id,
        stageName: 'The Weeknd',
        bio: 'Canadian singer, songwriter, and record producer known for his dark, atmospheric R&B music.',
        genre: 'R&B, Pop',
        profileImage: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400',
        coverImage: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745?w=800',
        socialLinks: {
          instagram: '@theweeknd',
          twitter: '@theweeknd',
          spotify: 'theweeknd'
        },
        isVerified: true,
        monthlyListeners: 85000000,
        totalPlays: 12000000000,
        followerCount: 45000000
      },
      {
        userId: users[2].id,
        stageName: 'Taylor Swift',
        bio: 'American singer-songwriter known for narrative songs about her personal life.',
        genre: 'Pop, Country, Folk',
        profileImage: 'https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?w=400',
        coverImage: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800',
        socialLinks: {
          instagram: '@taylorswift',
          twitter: '@taylorswift13',
          spotify: 'taylorswift'
        },
        isVerified: true,
        monthlyListeners: 95000000,
        totalPlays: 15000000000,
        followerCount: 52000000
      }
    ]);

    console.log('✅ Artists created');

    // Create sample songs
    const songs = await Song.bulkCreate([
      {
        artistId: artists[0].id,
        title: 'Blinding Lights',
        duration: 200,
        audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
        coverArt: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400',
        genre: 'Synthpop',
        album: 'After Hours',
        releaseDate: new Date('2019-11-29'),
        isPublic: true,
        isFeatured: true,
        playCount: 2800000000,
        downloadCount: 150000,
        likeCount: 45000000,
        tags: ['synthpop', 'retro', 'dance']
      },
      {
        artistId: artists[0].id,
        title: 'Save Your Tears',
        duration: 215,
        audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-2.mp3',
        coverArt: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400',
        genre: 'R&B',
        album: 'After Hours',
        releaseDate: new Date('2020-03-20'),
        isPublic: true,
        isFeatured: true,
        playCount: 1900000000,
        downloadCount: 120000,
        likeCount: 32000000,
        tags: ['r&b', 'emotional', 'ballad']
      },
      {
        artistId: artists[1].id,
        title: 'Anti-Hero',
        duration: 201,
        audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-3.mp3',
        coverArt: 'https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?w=400',
        genre: 'Pop',
        album: 'Midnights',
        releaseDate: new Date('2022-10-21'),
        isPublic: true,
        isFeatured: true,
        playCount: 1500000000,
        downloadCount: 200000,
        likeCount: 28000000,
        tags: ['pop', 'introspective', 'synth']
      },
      {
        artistId: artists[1].id,
        title: 'Lavender Haze',
        duration: 202,
        audioUrl: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-4.mp3',
        coverArt: 'https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?w=400',
        genre: 'Dream Pop',
        album: 'Midnights',
        releaseDate: new Date('2022-10-21'),
        isPublic: true,
        playCount: 800000000,
        downloadCount: 95000,
        likeCount: 18000000,
        tags: ['dream-pop', 'ethereal', 'love']
      }
    ]);

    console.log('✅ Songs created');

    // Create sample playlists
    const playlists = await Playlist.bulkCreate([
      {
        userId: users[3].id,
        name: 'My Favorites',
        description: 'All my favorite tracks in one place',
        coverImage: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400',
        isPublic: true,
        playCount: 15000,
        followerCount: 2500
      },
      {
        userId: users[1].id,
        name: 'The Weeknd Essentials',
        description: 'Essential tracks from The Weeknd',
        coverImage: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745?w=400',
        isPublic: true,
        playCount: 50000,
        followerCount: 12000
      }
    ]);

    console.log('✅ Playlists created');

    // Add songs to playlists
    await playlists[0].addSongs([songs[0], songs[2]]);
    await playlists[1].addSongs([songs[0], songs[1]]);

    console.log('✅ Songs added to playlists');

    // Create sample likes
    await Like.bulkCreate([
      { userId: users[3].id, entityType: 'song', entityId: songs[0].id },
      { userId: users[3].id, entityType: 'song', entityId: songs[2].id },
      { userId: users[3].id, entityType: 'artist', entityId: artists[0].id },
      { userId: users[1].id, entityType: 'song', entityId: songs[2].id }
    ]);

    console.log('✅ Likes created');

    // Create sample comments
    await Comment.bulkCreate([
      {
        userId: users[3].id,
        entityType: 'song',
        entityId: songs[0].id,
        content: 'This song is absolutely amazing! Can\'t stop listening to it.'
      },
      {
        userId: users[1].id,
        entityType: 'song',
        entityId: songs[2].id,
        content: 'Taylor Swift never disappoints! Love this track.'
      }
    ]);

    console.log('✅ Comments created');
    console.log('🎉 Database seeding completed successfully!');
    
    console.log('\n📊 Sample Data Summary:');
    console.log(`- ${users.length} users created`);
    console.log(`- ${artists.length} artists created`);
    console.log(`- ${songs.length} songs created`);
    console.log(`- ${playlists.length} playlists created`);
    console.log('\n🔐 Login Credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Artist (The Weeknd): <EMAIL> / artist123');
    console.log('Artist (Taylor Swift): <EMAIL> / artist123');
    console.log('User: <EMAIL> / user123');

  } catch (error) {
    console.error('❌ Seeding failed:', error);
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedData().then(() => process.exit(0));
}

module.exports = seedData;
