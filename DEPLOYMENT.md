# Deployment Guide for We Down Stars

## Render Deployment

This project is configured for easy deployment on Render using the included `render.yaml` file.

### Prerequisites

1. A Render account (free tier available)
2. A GitHub repository with your code
3. Basic understanding of environment variables

### Deployment Steps

#### 1. Database Setup

1. Go to Render Dashboard
2. Create a new PostgreSQL database:
   - Name: `wedownstars-db`
   - Database Name: `wedownstars`
   - User: `wedownstars_user`
   - Plan: Free
   - Region: Oregon (or your preferred region)

#### 2. Backend API Deployment

1. Create a new Web Service:
   - Name: `wedownstars-api`
   - Environment: Node
   - Build Command: `cd server && npm install`
   - Start Command: `cd server && npm start`
   - Plan: Free

2. Set Environment Variables:
   ```
   NODE_ENV=production
   DATABASE_URL=[Auto-filled from database connection]
   JWT_SECRET=[Generate a secure random string]
   PORT=10000
   ```

#### 3. Frontend Deployment

1. Create a new Static Site:
   - Name: `wedownstars-frontend`
   - Build Command: `cd client && npm install && npm run build`
   - Publish Directory: `./client/build`

2. Set Environment Variables:
   ```
   REACT_APP_API_URL=https://wedownstars-api.onrender.com/api
   ```

#### 4. Database Migration

After the backend is deployed:

1. Access the backend service shell
2. Run: `npm run migrate`
3. Run: `npm run seed` (to populate with sample data)

### Alternative: One-Click Deploy

You can also use the `render.yaml` file for one-click deployment:

1. Fork this repository
2. Connect your GitHub account to Render
3. Create a new Blueprint
4. Select your forked repository
5. Render will automatically create all services based on `render.yaml`

### Environment Variables Reference

#### Backend (.env)
```
NODE_ENV=production
PORT=10000
DATABASE_URL=postgresql://username:password@host:port/database
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
CLOUDINARY_CLOUD_NAME=your-cloud-name (optional)
CLOUDINARY_API_KEY=your-api-key (optional)
CLOUDINARY_API_SECRET=your-api-secret (optional)
```

#### Frontend (.env)
```
REACT_APP_API_URL=https://your-backend-url.onrender.com/api
```

### Post-Deployment

1. Visit your frontend URL
2. Test the demo accounts:
   - Admin: `<EMAIL>` / `admin123`
   - Artist: `<EMAIL>` / `artist123`
   - User: `<EMAIL>` / `user123`

### Troubleshooting

#### Common Issues

1. **Database Connection Failed**
   - Verify DATABASE_URL is correctly set
   - Check if database service is running

2. **Frontend Can't Connect to API**
   - Verify REACT_APP_API_URL points to correct backend URL
   - Check CORS settings in backend

3. **Build Failures**
   - Check build logs for specific errors
   - Verify all dependencies are in package.json
   - Ensure Node.js version compatibility

#### Logs

- Backend logs: Available in Render dashboard under your web service
- Frontend logs: Available during build process
- Database logs: Available in database service dashboard

### Scaling

For production use, consider:

1. Upgrading to paid plans for better performance
2. Setting up custom domains
3. Implementing CDN for static assets
4. Adding monitoring and alerting
5. Setting up automated backups

### Security Notes

1. Always use strong, unique JWT secrets
2. Enable HTTPS (automatic on Render)
3. Regularly update dependencies
4. Monitor for security vulnerabilities
5. Implement rate limiting for production

### Support

For deployment issues:
1. Check Render documentation
2. Review application logs
3. Verify environment variables
4. Test locally first
