const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { Song, Artist, User, Like, Comment, PlayHistory } = require('../models');
const { authenticateToken, requireArtist, optionalAuth } = require('../middleware/auth');
const { Op } = require('sequelize');

const router = express.Router();

// Get all songs (public endpoint with optional auth)
router.get('/', optionalAuth, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('genre').optional().isString(),
  query('featured').optional().isBoolean(),
  query('sort').optional().isIn(['newest', 'oldest', 'popular', 'trending'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const { genre, featured, sort = 'newest' } = req.query;

    // Build where clause
    const where = { isPublic: true };
    if (genre) where.genre = { [Op.iLike]: `%${genre}%` };
    if (featured === 'true') where.isFeatured = true;

    // Build order clause
    let order = [];
    switch (sort) {
      case 'oldest':
        order = [['createdAt', 'ASC']];
        break;
      case 'popular':
        order = [['playCount', 'DESC']];
        break;
      case 'trending':
        order = [['likeCount', 'DESC'], ['playCount', 'DESC']];
        break;
      default:
        order = [['createdAt', 'DESC']];
    }

    const { count, rows: songs } = await Song.findAndCountAll({
      where,
      include: [{
        model: Artist,
        as: 'artist',
        attributes: ['id', 'stageName', 'profileImage', 'isVerified']
      }],
      order,
      limit,
      offset
    });

    // If user is authenticated, check which songs they've liked
    let likedSongs = [];
    if (req.user) {
      const likes = await Like.findAll({
        where: {
          userId: req.user.id,
          entityType: 'song',
          entityId: { [Op.in]: songs.map(song => song.id) }
        }
      });
      likedSongs = likes.map(like => like.entityId);
    }

    const songsWithLikeStatus = songs.map(song => ({
      ...song.toJSON(),
      isLiked: likedSongs.includes(song.id)
    }));

    res.json({
      songs: songsWithLikeStatus,
      pagination: {
        page,
        limit,
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Get songs error:', error);
    res.status(500).json({ error: 'Failed to fetch songs' });
  }
});

// Get single song by ID
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const song = await Song.findByPk(req.params.id, {
      include: [{
        model: Artist,
        as: 'artist',
        attributes: ['id', 'stageName', 'profileImage', 'isVerified', 'bio']
      }]
    });

    if (!song) {
      return res.status(404).json({ error: 'Song not found' });
    }

    // Check if song is public or user has access
    if (!song.isPublic && (!req.user || (req.user.role !== 'admin' && song.artist.userId !== req.user.id))) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Check if user has liked this song
    let isLiked = false;
    if (req.user) {
      const like = await Like.findOne({
        where: {
          userId: req.user.id,
          entityType: 'song',
          entityId: song.id
        }
      });
      isLiked = !!like;
    }

    res.json({
      ...song.toJSON(),
      isLiked
    });
  } catch (error) {
    console.error('Get song error:', error);
    res.status(500).json({ error: 'Failed to fetch song' });
  }
});

// Create new song (artists only)
router.post('/', authenticateToken, requireArtist, [
  body('title').notEmpty().withMessage('Title is required'),
  body('duration').isInt({ min: 1 }).withMessage('Duration must be a positive integer'),
  body('audioUrl').isURL().withMessage('Valid audio URL is required'),
  body('genre').optional().isString(),
  body('album').optional().isString(),
  body('coverArt').optional().isURL(),
  body('lyrics').optional().isString(),
  body('tags').optional().isArray()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: 'Validation failed',
        details: errors.array()
      });
    }

    // Get artist profile
    const artist = await Artist.findOne({ where: { userId: req.user.id } });
    if (!artist) {
      return res.status(404).json({ error: 'Artist profile not found' });
    }

    const songData = {
      ...req.body,
      artistId: artist.id
    };

    const song = await Song.create(songData);

    // Fetch the created song with artist info
    const createdSong = await Song.findByPk(song.id, {
      include: [{
        model: Artist,
        as: 'artist',
        attributes: ['id', 'stageName', 'profileImage', 'isVerified']
      }]
    });

    res.status(201).json({
      message: 'Song created successfully',
      song: createdSong
    });
  } catch (error) {
    console.error('Create song error:', error);
    res.status(500).json({ error: 'Failed to create song' });
  }
});

// Update song (artist/admin only)
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const song = await Song.findByPk(req.params.id, {
      include: [{
        model: Artist,
        as: 'artist'
      }]
    });

    if (!song) {
      return res.status(404).json({ error: 'Song not found' });
    }

    // Check permissions
    if (req.user.role !== 'admin' && song.artist.userId !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const allowedUpdates = ['title', 'genre', 'album', 'coverArt', 'lyrics', 'tags', 'isPublic'];
    const updateData = {};
    
    allowedUpdates.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    await song.update(updateData);

    const updatedSong = await Song.findByPk(song.id, {
      include: [{
        model: Artist,
        as: 'artist',
        attributes: ['id', 'stageName', 'profileImage', 'isVerified']
      }]
    });

    res.json({
      message: 'Song updated successfully',
      song: updatedSong
    });
  } catch (error) {
    console.error('Update song error:', error);
    res.status(500).json({ error: 'Failed to update song' });
  }
});

// Delete song (artist/admin only)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const song = await Song.findByPk(req.params.id, {
      include: [{
        model: Artist,
        as: 'artist'
      }]
    });

    if (!song) {
      return res.status(404).json({ error: 'Song not found' });
    }

    // Check permissions
    if (req.user.role !== 'admin' && song.artist.userId !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    await song.destroy();

    res.json({ message: 'Song deleted successfully' });
  } catch (error) {
    console.error('Delete song error:', error);
    res.status(500).json({ error: 'Failed to delete song' });
  }
});

// Like/Unlike song
router.post('/:id/like', authenticateToken, async (req, res) => {
  try {
    const song = await Song.findByPk(req.params.id);
    if (!song) {
      return res.status(404).json({ error: 'Song not found' });
    }

    const existingLike = await Like.findOne({
      where: {
        userId: req.user.id,
        entityType: 'song',
        entityId: song.id
      }
    });

    if (existingLike) {
      // Unlike
      await existingLike.destroy();
      await song.decrement('likeCount');
      res.json({ message: 'Song unliked', isLiked: false });
    } else {
      // Like
      await Like.create({
        userId: req.user.id,
        entityType: 'song',
        entityId: song.id
      });
      await song.increment('likeCount');
      res.json({ message: 'Song liked', isLiked: true });
    }
  } catch (error) {
    console.error('Like song error:', error);
    res.status(500).json({ error: 'Failed to like/unlike song' });
  }
});

// Record play
router.post('/:id/play', optionalAuth, async (req, res) => {
  try {
    const song = await Song.findByPk(req.params.id);
    if (!song) {
      return res.status(404).json({ error: 'Song not found' });
    }

    // Increment play count
    await song.increment('playCount');

    // Record in play history if user is authenticated
    if (req.user) {
      await PlayHistory.create({
        userId: req.user.id,
        songId: song.id,
        duration: req.body.duration || 0
      });
    }

    res.json({ message: 'Play recorded' });
  } catch (error) {
    console.error('Record play error:', error);
    res.status(500).json({ error: 'Failed to record play' });
  }
});

module.exports = router;
