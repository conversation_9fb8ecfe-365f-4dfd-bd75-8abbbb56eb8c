const { sequelize } = require('../config/database');

// Import all models
const User = require('./User');
const Artist = require('./Artist');
const Song = require('./Song');
const Playlist = require('./Playlist');
const Like = require('./Like');
const Comment = require('./Comment');

// Define associations
// User-Artist relationship (one-to-one)
User.hasOne(Artist, { foreignKey: 'userId', as: 'artistProfile' });
Artist.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Artist-Song relationship (one-to-many)
Artist.hasMany(Song, { foreignKey: 'artistId', as: 'songs' });
Song.belongsTo(Artist, { foreignKey: 'artistId', as: 'artist' });

// User-Playlist relationship (one-to-many)
User.hasMany(Playlist, { foreignKey: 'userId', as: 'playlists' });
Playlist.belongsTo(User, { foreignKey: 'userId', as: 'creator' });

// Playlist-Song relationship (many-to-many)
const PlaylistSong = sequelize.define('PlaylistSong', {
  position: {
    type: sequelize.Sequelize.INTEGER,
    defaultValue: 0
  },
  addedAt: {
    type: sequelize.Sequelize.DATE,
    defaultValue: sequelize.Sequelize.NOW
  }
}, {
  timestamps: false
});

Playlist.belongsToMany(Song, { 
  through: PlaylistSong, 
  foreignKey: 'playlistId',
  as: 'songs'
});
Song.belongsToMany(Playlist, { 
  through: PlaylistSong, 
  foreignKey: 'songId',
  as: 'playlists'
});

// User-Like relationship (one-to-many)
User.hasMany(Like, { foreignKey: 'userId', as: 'likes' });
Like.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// User-Comment relationship (one-to-many)
User.hasMany(Comment, { foreignKey: 'userId', as: 'comments' });
Comment.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// User-User following relationship (many-to-many)
const Follow = sequelize.define('Follow', {
  followedAt: {
    type: sequelize.Sequelize.DATE,
    defaultValue: sequelize.Sequelize.NOW
  }
}, {
  timestamps: false
});

User.belongsToMany(User, {
  through: Follow,
  as: 'following',
  foreignKey: 'followerId',
  otherKey: 'followingId'
});

User.belongsToMany(User, {
  through: Follow,
  as: 'followers',
  foreignKey: 'followingId',
  otherKey: 'followerId'
});

// Play history tracking
const PlayHistory = sequelize.define('PlayHistory', {
  id: {
    type: sequelize.Sequelize.UUID,
    defaultValue: sequelize.Sequelize.UUIDV4,
    primaryKey: true
  },
  playedAt: {
    type: sequelize.Sequelize.DATE,
    defaultValue: sequelize.Sequelize.NOW
  },
  duration: {
    type: sequelize.Sequelize.INTEGER, // seconds played
    defaultValue: 0
  }
}, {
  timestamps: false
});

User.hasMany(PlayHistory, { foreignKey: 'userId', as: 'playHistory' });
PlayHistory.belongsTo(User, { foreignKey: 'userId', as: 'user' });

Song.hasMany(PlayHistory, { foreignKey: 'songId', as: 'playHistory' });
PlayHistory.belongsTo(Song, { foreignKey: 'songId', as: 'song' });

module.exports = {
  sequelize,
  User,
  Artist,
  Song,
  Playlist,
  Like,
  Comment,
  PlaylistSong,
  Follow,
  PlayHistory
};
