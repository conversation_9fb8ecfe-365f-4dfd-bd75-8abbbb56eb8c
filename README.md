# We Down Stars 🎵

A modern, responsive music streaming platform for artists and fans.

## Features

### Admin Dashboard
- Artist profile creation & management
- Song upload & management with cover art
- Analytics dashboard (plays, downloads, likes, revenue)
- Content visibility controls (public/private)
- Role-based access (Admin & Artist roles)

### Public Website
- Home page with featured artists & trending songs
- Artist profiles with discography
- Advanced music player with queue management
- Search & filter functionality
- User playlists, likes, comments, and downloads
- Dark/light mode toggle
- Mobile-first responsive design

## Tech Stack

- **Frontend**: React 18 + TailwindCSS + Vite
- **Backend**: Node.js + Express + JWT Authentication
- **Database**: MongoDB with Mongoose
- **File Storage**: Local storage (easily expandable to AWS S3)
- **Audio Streaming**: Optimized for web audio playback

## Quick Start

1. **Install all dependencies**:
   ```bash
   npm run install-all
   ```

2. **Start development servers**:
   ```bash
   npm run dev
   ```

3. **Access the application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## Project Structure

```
we-down-stars/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── hooks/         # Custom React hooks
│   │   ├── context/       # React context providers
│   │   ├── utils/         # Utility functions
│   │   └── assets/        # Static assets
├── server/                # Node.js backend
│   ├── models/           # Database models
│   ├── routes/           # API routes
│   ├── middleware/       # Custom middleware
│   ├── controllers/      # Route controllers
│   ├── uploads/          # File upload storage
│   └── utils/            # Server utilities
└── shared/               # Shared types/constants
```

## Environment Variables

Create `.env` files in both `client/` and `server/` directories:

### Server (.env)
```
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb://localhost:27017/wedownstars
JWT_SECRET=your-super-secret-jwt-key
UPLOAD_PATH=./uploads
```

### Client (.env)
```
REACT_APP_API_URL=http://localhost:5000/api
```

## Demo Content

The platform includes sample artists, songs, and playlists to demonstrate functionality.

## License

MIT License - see LICENSE file for details.
