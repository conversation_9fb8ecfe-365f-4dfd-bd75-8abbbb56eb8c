const express = require('express');
const { User, Artist, Playlist, Like } = require('../models');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get user's liked songs
router.get('/likes/songs', authenticateToken, async (req, res) => {
  try {
    const likes = await Like.findAll({
      where: {
        userId: req.user.id,
        entityType: 'song'
      },
      include: [{
        model: Song,
        as: 'song',
        include: [{
          model: Artist,
          as: 'artist',
          attributes: ['id', 'stageName', 'profileImage']
        }]
      }],
      order: [['createdAt', 'DESC']]
    });

    res.json({ likedSongs: likes.map(like => like.song) });
  } catch (error) {
    console.error('Get liked songs error:', error);
    res.status(500).json({ error: 'Failed to fetch liked songs' });
  }
});

// Get user's playlists
router.get('/playlists', authenticateToken, async (req, res) => {
  try {
    const playlists = await Playlist.findAll({
      where: { userId: req.user.id },
      order: [['createdAt', 'DESC']]
    });

    res.json({ playlists });
  } catch (error) {
    console.error('Get user playlists error:', error);
    res.status(500).json({ error: 'Failed to fetch playlists' });
  }
});

module.exports = router;
