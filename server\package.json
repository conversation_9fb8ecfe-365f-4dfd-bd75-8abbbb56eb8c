{"name": "we-down-stars-server", "version": "1.0.0", "description": "Backend API for We Down Stars music platform", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "seed": "node scripts/seedData.js", "migrate": "node scripts/migrate.js"}, "dependencies": {"express": "^4.18.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.32.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "express-validator": "^7.0.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "compression": "^1.7.4", "morgan": "^1.10.0", "cloudinary": "^1.40.0"}, "devDependencies": {"nodemon": "^3.0.1", "sequelize-cli": "^6.6.1"}, "keywords": ["music", "api", "streaming", "backend", "postgresql"], "author": "We Down Stars Team", "license": "MIT"}