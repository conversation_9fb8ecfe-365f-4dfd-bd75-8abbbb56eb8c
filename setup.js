#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎵 Setting up We Down Stars...\n');

// Create environment files
const createEnvFiles = () => {
  console.log('📝 Creating environment files...');
  
  // Server .env
  const serverEnv = `NODE_ENV=development
PORT=5000
DATABASE_URL=postgresql://localhost:5432/wedownstars
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-minimum-32-chars
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
CORS_ORIGINS=http://localhost:3000`;

  fs.writeFileSync(path.join(__dirname, 'server', '.env'), serverEnv);
  
  // Client .env
  const clientEnv = `REACT_APP_API_URL=http://localhost:5000/api`;
  fs.writeFileSync(path.join(__dirname, 'client', '.env'), clientEnv);
  
  console.log('✅ Environment files created');
};

// Install dependencies
const installDependencies = () => {
  console.log('📦 Installing dependencies...');
  
  try {
    console.log('Installing root dependencies...');
    execSync('npm install', { stdio: 'inherit' });
    
    console.log('Installing server dependencies...');
    execSync('cd server && npm install', { stdio: 'inherit' });
    
    console.log('Installing client dependencies...');
    execSync('cd client && npm install', { stdio: 'inherit' });
    
    console.log('✅ All dependencies installed');
  } catch (error) {
    console.error('❌ Failed to install dependencies:', error.message);
    process.exit(1);
  }
};

// Create upload directories
const createDirectories = () => {
  console.log('📁 Creating upload directories...');
  
  const dirs = [
    'server/uploads',
    'server/uploads/audio',
    'server/uploads/images'
  ];
  
  dirs.forEach(dir => {
    const fullPath = path.join(__dirname, dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
    }
  });
  
  console.log('✅ Upload directories created');
};

// Main setup function
const setup = async () => {
  try {
    createEnvFiles();
    createDirectories();
    installDependencies();
    
    console.log('\n🎉 Setup completed successfully!\n');
    console.log('📋 Next steps:');
    console.log('1. Set up PostgreSQL database:');
    console.log('   - Install PostgreSQL');
    console.log('   - Create database: createdb wedownstars');
    console.log('   - Update DATABASE_URL in server/.env if needed');
    console.log('');
    console.log('2. Run database migration:');
    console.log('   cd server && npm run migrate');
    console.log('');
    console.log('3. Seed sample data:');
    console.log('   cd server && npm run seed');
    console.log('');
    console.log('4. Start development servers:');
    console.log('   npm run dev');
    console.log('');
    console.log('🌐 The app will be available at:');
    console.log('   Frontend: http://localhost:3000');
    console.log('   Backend:  http://localhost:5000');
    console.log('');
    console.log('🔐 Demo accounts:');
    console.log('   Admin:  <EMAIL> / admin123');
    console.log('   Artist: <EMAIL> / artist123');
    console.log('   User:   <EMAIL> / user123');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
};

// Run setup
setup();
