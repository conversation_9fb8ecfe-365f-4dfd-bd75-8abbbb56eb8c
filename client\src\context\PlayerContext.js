import React, { createContext, useContext, useReducer, useRef, useEffect } from 'react';

const PlayerContext = createContext();

const initialState = {
  currentSong: null,
  queue: [],
  currentIndex: 0,
  isPlaying: false,
  volume: 0.7,
  isMuted: false,
  currentTime: 0,
  duration: 0,
  isLoading: false,
  repeat: 'none', // 'none', 'one', 'all'
  shuffle: false,
  originalQueue: [], // For shuffle mode
};

const playerReducer = (state, action) => {
  switch (action.type) {
    case 'SET_CURRENT_SONG':
      return {
        ...state,
        currentSong: action.payload.song,
        queue: action.payload.queue || [action.payload.song],
        currentIndex: action.payload.index || 0,
        originalQueue: action.payload.queue || [action.payload.song],
      };
    case 'SET_QUEUE':
      return {
        ...state,
        queue: action.payload,
        originalQueue: action.payload,
      };
    case 'PLAY':
      return { ...state, isPlaying: true };
    case 'PAUSE':
      return { ...state, isPlaying: false };
    case 'SET_VOLUME':
      return { ...state, volume: action.payload };
    case 'TOGGLE_MUTE':
      return { ...state, isMuted: !state.isMuted };
    case 'SET_CURRENT_TIME':
      return { ...state, currentTime: action.payload };
    case 'SET_DURATION':
      return { ...state, duration: action.payload };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'NEXT_SONG':
      const nextIndex = state.currentIndex + 1;
      if (nextIndex < state.queue.length) {
        return {
          ...state,
          currentIndex: nextIndex,
          currentSong: state.queue[nextIndex],
        };
      } else if (state.repeat === 'all') {
        return {
          ...state,
          currentIndex: 0,
          currentSong: state.queue[0],
        };
      }
      return state;
    case 'PREVIOUS_SONG':
      const prevIndex = state.currentIndex - 1;
      if (prevIndex >= 0) {
        return {
          ...state,
          currentIndex: prevIndex,
          currentSong: state.queue[prevIndex],
        };
      } else if (state.repeat === 'all') {
        const lastIndex = state.queue.length - 1;
        return {
          ...state,
          currentIndex: lastIndex,
          currentSong: state.queue[lastIndex],
        };
      }
      return state;
    case 'SET_REPEAT':
      return { ...state, repeat: action.payload };
    case 'TOGGLE_SHUFFLE':
      if (!state.shuffle) {
        // Enable shuffle
        const shuffledQueue = [...state.queue];
        const currentSong = shuffledQueue[state.currentIndex];
        
        // Remove current song and shuffle the rest
        shuffledQueue.splice(state.currentIndex, 1);
        for (let i = shuffledQueue.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [shuffledQueue[i], shuffledQueue[j]] = [shuffledQueue[j], shuffledQueue[i]];
        }
        
        // Put current song at the beginning
        shuffledQueue.unshift(currentSong);
        
        return {
          ...state,
          shuffle: true,
          queue: shuffledQueue,
          currentIndex: 0,
        };
      } else {
        // Disable shuffle - restore original queue
        const currentSong = state.currentSong;
        const originalIndex = state.originalQueue.findIndex(song => song.id === currentSong.id);
        
        return {
          ...state,
          shuffle: false,
          queue: state.originalQueue,
          currentIndex: originalIndex >= 0 ? originalIndex : 0,
        };
      }
    default:
      return state;
  }
};

export const PlayerProvider = ({ children }) => {
  const [state, dispatch] = useReducer(playerReducer, initialState);
  const audioRef = useRef(null);

  // Initialize audio element
  useEffect(() => {
    audioRef.current = new Audio();
    audioRef.current.volume = state.volume;
    
    const audio = audioRef.current;

    const handleLoadStart = () => dispatch({ type: 'SET_LOADING', payload: true });
    const handleCanPlay = () => dispatch({ type: 'SET_LOADING', payload: false });
    const handleLoadedMetadata = () => {
      dispatch({ type: 'SET_DURATION', payload: audio.duration });
    };
    const handleTimeUpdate = () => {
      dispatch({ type: 'SET_CURRENT_TIME', payload: audio.currentTime });
    };
    const handleEnded = () => {
      if (state.repeat === 'one') {
        audio.currentTime = 0;
        audio.play();
      } else {
        nextSong();
      }
    };

    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
    };
  }, []);

  // Update audio source when current song changes
  useEffect(() => {
    if (state.currentSong && audioRef.current) {
      audioRef.current.src = `${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${state.currentSong.audioUrl}`;
      audioRef.current.load();
    }
  }, [state.currentSong]);

  // Update audio volume
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = state.isMuted ? 0 : state.volume;
    }
  }, [state.volume, state.isMuted]);

  // Play/pause audio
  useEffect(() => {
    if (audioRef.current) {
      if (state.isPlaying) {
        audioRef.current.play().catch(console.error);
      } else {
        audioRef.current.pause();
      }
    }
  }, [state.isPlaying]);

  const playSong = (song, queue = null, index = 0) => {
    dispatch({
      type: 'SET_CURRENT_SONG',
      payload: { song, queue, index },
    });
    dispatch({ type: 'PLAY' });
  };

  const play = () => dispatch({ type: 'PLAY' });
  const pause = () => dispatch({ type: 'PAUSE' });
  
  const togglePlayPause = () => {
    if (state.isPlaying) {
      pause();
    } else {
      play();
    }
  };

  const nextSong = () => {
    dispatch({ type: 'NEXT_SONG' });
  };

  const previousSong = () => {
    dispatch({ type: 'PREVIOUS_SONG' });
  };

  const setVolume = (volume) => {
    dispatch({ type: 'SET_VOLUME', payload: Math.max(0, Math.min(1, volume)) });
  };

  const toggleMute = () => {
    dispatch({ type: 'TOGGLE_MUTE' });
  };

  const seek = (time) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time;
      dispatch({ type: 'SET_CURRENT_TIME', payload: time });
    }
  };

  const setRepeat = (mode) => {
    dispatch({ type: 'SET_REPEAT', payload: mode });
  };

  const toggleShuffle = () => {
    dispatch({ type: 'TOGGLE_SHUFFLE' });
  };

  const addToQueue = (songs) => {
    const newQueue = [...state.queue, ...songs];
    dispatch({ type: 'SET_QUEUE', payload: newQueue });
  };

  const value = {
    ...state,
    playSong,
    play,
    pause,
    togglePlayPause,
    nextSong,
    previousSong,
    setVolume,
    toggleMute,
    seek,
    setRepeat,
    toggleShuffle,
    addToQueue,
  };

  return (
    <PlayerContext.Provider value={value}>
      {children}
    </PlayerContext.Provider>
  );
};

export const usePlayer = () => {
  const context = useContext(PlayerContext);
  if (!context) {
    throw new Error('usePlayer must be used within a PlayerProvider');
  }
  return context;
};
