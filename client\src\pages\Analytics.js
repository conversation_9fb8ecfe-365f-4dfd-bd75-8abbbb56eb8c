import React from 'react';
import { BarChart3, TrendingUp } from 'lucide-react';

const Analytics = () => {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Analytics
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Track your music performance and audience insights.
        </p>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="text-center py-12">
          <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Analytics Dashboard
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            Detailed analytics including play counts, listener demographics, 
            revenue tracking, and performance insights will be available here.
          </p>
          <div className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700">
            <TrendingUp className="h-4 w-4 mr-2" />
            Coming Soon
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
