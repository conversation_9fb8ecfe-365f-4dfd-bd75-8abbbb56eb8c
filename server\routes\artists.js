const express = require('express');
const { query, body, validationResult } = require('express-validator');
const { Artist, User, Song, Like } = require('../models');
const { authenticateToken, requireArtist, optionalAuth } = require('../middleware/auth');
const { Op } = require('sequelize');

const router = express.Router();

// Get all artists
router.get('/', optionalAuth, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 50 }),
  query('genre').optional().isString(),
  query('verified').optional().isBoolean(),
  query('sort').optional().isIn(['newest', 'popular', 'followers'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const { genre, verified, sort = 'newest' } = req.query;

    const where = { isActive: true };
    if (genre) where.genre = { [Op.iLike]: `%${genre}%` };
    if (verified === 'true') where.isVerified = true;

    let order = [];
    switch (sort) {
      case 'popular':
        order = [['totalPlays', 'DESC']];
        break;
      case 'followers':
        order = [['followerCount', 'DESC']];
        break;
      default:
        order = [['createdAt', 'DESC']];
    }

    const { count, rows: artists } = await Artist.findAndCountAll({
      where,
      order,
      limit,
      offset,
      attributes: { exclude: ['userId'] }
    });

    res.json({
      artists,
      pagination: {
        page,
        limit,
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Get artists error:', error);
    res.status(500).json({ error: 'Failed to fetch artists' });
  }
});

// Get single artist by ID
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const artist = await Artist.findByPk(req.params.id, {
      include: [{
        model: Song,
        as: 'songs',
        where: { isPublic: true },
        required: false,
        order: [['createdAt', 'DESC']],
        limit: 10
      }]
    });

    if (!artist || !artist.isActive) {
      return res.status(404).json({ error: 'Artist not found' });
    }

    // Check if user follows this artist
    let isFollowing = false;
    if (req.user) {
      const like = await Like.findOne({
        where: {
          userId: req.user.id,
          entityType: 'artist',
          entityId: artist.id
        }
      });
      isFollowing = !!like;
    }

    res.json({
      ...artist.toJSON(),
      isFollowing
    });
  } catch (error) {
    console.error('Get artist error:', error);
    res.status(500).json({ error: 'Failed to fetch artist' });
  }
});

// Get artist's songs
router.get('/:id/songs', optionalAuth, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 50 })
], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    const artist = await Artist.findByPk(req.params.id);
    if (!artist) {
      return res.status(404).json({ error: 'Artist not found' });
    }

    const { count, rows: songs } = await Song.findAndCountAll({
      where: { 
        artistId: artist.id,
        isPublic: true
      },
      include: [{
        model: Artist,
        as: 'artist',
        attributes: ['id', 'stageName', 'profileImage', 'isVerified']
      }],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    res.json({
      songs,
      pagination: {
        page,
        limit,
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Get artist songs error:', error);
    res.status(500).json({ error: 'Failed to fetch artist songs' });
  }
});

// Update artist profile (artist/admin only)
router.put('/:id', authenticateToken, [
  body('stageName').optional().isLength({ min: 1, max: 100 }),
  body('bio').optional().isLength({ max: 2000 }),
  body('genre').optional().isString(),
  body('socialLinks').optional().isObject()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const artist = await Artist.findByPk(req.params.id);
    if (!artist) {
      return res.status(404).json({ error: 'Artist not found' });
    }

    // Check permissions
    if (req.user.role !== 'admin' && artist.userId !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const allowedUpdates = ['stageName', 'bio', 'genre', 'profileImage', 'coverImage', 'socialLinks'];
    const updateData = {};
    
    allowedUpdates.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    await artist.update(updateData);

    res.json({
      message: 'Artist profile updated successfully',
      artist
    });
  } catch (error) {
    console.error('Update artist error:', error);
    res.status(500).json({ error: 'Failed to update artist profile' });
  }
});

// Follow/Unfollow artist
router.post('/:id/follow', authenticateToken, async (req, res) => {
  try {
    const artist = await Artist.findByPk(req.params.id);
    if (!artist) {
      return res.status(404).json({ error: 'Artist not found' });
    }

    // Can't follow yourself
    if (artist.userId === req.user.id) {
      return res.status(400).json({ error: 'Cannot follow yourself' });
    }

    const existingFollow = await Like.findOne({
      where: {
        userId: req.user.id,
        entityType: 'artist',
        entityId: artist.id
      }
    });

    if (existingFollow) {
      // Unfollow
      await existingFollow.destroy();
      await artist.decrement('followerCount');
      res.json({ message: 'Artist unfollowed', isFollowing: false });
    } else {
      // Follow
      await Like.create({
        userId: req.user.id,
        entityType: 'artist',
        entityId: artist.id
      });
      await artist.increment('followerCount');
      res.json({ message: 'Artist followed', isFollowing: true });
    }
  } catch (error) {
    console.error('Follow artist error:', error);
    res.status(500).json({ error: 'Failed to follow/unfollow artist' });
  }
});

// Get artist analytics (artist/admin only)
router.get('/:id/analytics', authenticateToken, async (req, res) => {
  try {
    const artist = await Artist.findByPk(req.params.id);
    if (!artist) {
      return res.status(404).json({ error: 'Artist not found' });
    }

    // Check permissions
    if (req.user.role !== 'admin' && artist.userId !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Get song statistics
    const songs = await Song.findAll({
      where: { artistId: artist.id },
      attributes: ['id', 'title', 'playCount', 'likeCount', 'downloadCount', 'createdAt']
    });

    const totalPlays = songs.reduce((sum, song) => sum + parseInt(song.playCount), 0);
    const totalLikes = songs.reduce((sum, song) => sum + song.likeCount, 0);
    const totalDownloads = songs.reduce((sum, song) => sum + song.downloadCount, 0);

    res.json({
      overview: {
        totalSongs: songs.length,
        totalPlays,
        totalLikes,
        totalDownloads,
        followerCount: artist.followerCount,
        monthlyListeners: artist.monthlyListeners
      },
      songs: songs.map(song => ({
        id: song.id,
        title: song.title,
        plays: parseInt(song.playCount),
        likes: song.likeCount,
        downloads: song.downloadCount,
        releaseDate: song.createdAt
      }))
    });
  } catch (error) {
    console.error('Get analytics error:', error);
    res.status(500).json({ error: 'Failed to fetch analytics' });
  }
});

module.exports = router;
