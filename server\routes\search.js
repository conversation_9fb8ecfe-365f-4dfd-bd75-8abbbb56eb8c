const express = require('express');
const { query, validationResult } = require('express-validator');
const { Song, Artist, Playlist, User } = require('../models');
const { optionalAuth } = require('../middleware/auth');
const { Op } = require('sequelize');

const router = express.Router();

// Search across songs, artists, and playlists
router.get('/', optionalAuth, [
  query('q').notEmpty().withMessage('Search query is required'),
  query('type').optional().isIn(['all', 'songs', 'artists', 'playlists']),
  query('limit').optional().isInt({ min: 1, max: 50 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { q: query, type = 'all', limit = 10 } = req.query;
    const searchTerm = `%${query}%`;
    const results = {};

    if (type === 'all' || type === 'songs') {
      const songs = await Song.findAll({
        where: {
          [Op.and]: [
            { isPublic: true },
            {
              [Op.or]: [
                { title: { [Op.iLike]: searchTerm } },
                { genre: { [Op.iLike]: searchTerm } },
                { album: { [Op.iLike]: searchTerm } }
              ]
            }
          ]
        },
        include: [{
          model: Artist,
          as: 'artist',
          attributes: ['id', 'stageName', 'profileImage']
        }],
        limit: parseInt(limit),
        order: [['playCount', 'DESC']]
      });
      results.songs = songs;
    }

    if (type === 'all' || type === 'artists') {
      const artists = await Artist.findAll({
        where: {
          [Op.and]: [
            { isActive: true },
            {
              [Op.or]: [
                { stageName: { [Op.iLike]: searchTerm } },
                { genre: { [Op.iLike]: searchTerm } }
              ]
            }
          ]
        },
        limit: parseInt(limit),
        order: [['followerCount', 'DESC']]
      });
      results.artists = artists;
    }

    if (type === 'all' || type === 'playlists') {
      const playlists = await Playlist.findAll({
        where: {
          [Op.and]: [
            { isPublic: true },
            { name: { [Op.iLike]: searchTerm } }
          ]
        },
        include: [{
          model: User,
          as: 'creator',
          attributes: ['id', 'username']
        }],
        limit: parseInt(limit),
        order: [['followerCount', 'DESC']]
      });
      results.playlists = playlists;
    }

    res.json(results);
  } catch (error) {
    console.error('Search error:', error);
    res.status(500).json({ error: 'Search failed' });
  }
});

module.exports = router;
