{"name": "we-down-stars", "version": "1.0.0", "description": "Modern music streaming platform for artists and fans", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "build": "cd client && npm run build", "install-all": "npm install && cd server && npm install && cd ../client && npm install", "start": "cd server && npm start"}, "keywords": ["music", "streaming", "platform", "react", "node"], "author": "We Down Stars Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}