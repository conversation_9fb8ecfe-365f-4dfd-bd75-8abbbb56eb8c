import React, { useState } from 'react';
import { 
  Play, 
  Pause, 
  SkipBack, 
  Ski<PERSON>For<PERSON>, 
  Volume2, 
  VolumeX,
  Repeat,
  Shuffle,
  Heart,
  Download
} from 'lucide-react';
import { usePlayer } from '../../context/PlayerContext';
import { useAuth } from '../../context/AuthContext';
import { songsAPI } from '../../utils/api';
import { toast } from 'react-hot-toast';

const MusicPlayer = () => {
  const {
    currentSong,
    isPlaying,
    volume,
    isMuted,
    currentTime,
    duration,
    repeat,
    shuffle,
    togglePlayPause,
    nextSong,
    previousSong,
    setVolume,
    toggleMute,
    seek,
    setRepeat,
    toggleShuffle,
  } = usePlayer();

  const { isAuthenticated } = useAuth();
  const [isLiked, setIsLiked] = useState(false);

  const formatTime = (time) => {
    if (isNaN(time)) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleProgressChange = (e) => {
    const newTime = (e.target.value / 100) * duration;
    seek(newTime);
  };

  const handleVolumeChange = (e) => {
    setVolume(e.target.value / 100);
  };

  const handleLike = async () => {
    if (!isAuthenticated) {
      toast.error('Please login to like songs');
      return;
    }

    try {
      await songsAPI.like(currentSong.id);
      setIsLiked(!isLiked);
      toast.success(isLiked ? 'Removed from favorites' : 'Added to favorites');
    } catch (error) {
      toast.error('Failed to update like status');
    }
  };

  const handleDownload = () => {
    if (!isAuthenticated) {
      toast.error('Please login to download songs');
      return;
    }

    // Create download link
    const link = document.createElement('a');
    link.href = `${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${currentSong.audioUrl}`;
    link.download = `${currentSong.artist.stageName} - ${currentSong.title}.mp3`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('Download started');
  };

  const getRepeatIcon = () => {
    if (repeat === 'one') return '1';
    return <Repeat className="h-4 w-4" />;
  };

  if (!currentSong) return null;

  return (
    <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 px-4 py-3">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        {/* Song Info */}
        <div className="flex items-center space-x-4 min-w-0 flex-1">
          <img
            src={currentSong.coverArt || '/default-cover.jpg'}
            alt={currentSong.title}
            className="w-12 h-12 rounded-md object-cover"
          />
          <div className="min-w-0">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {currentSong.title}
            </h4>
            <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
              {currentSong.artist.stageName}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleLike}
              className={`p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 ${
                isLiked ? 'text-red-500' : 'text-gray-400'
              }`}
            >
              <Heart className="h-4 w-4" fill={isLiked ? 'currentColor' : 'none'} />
            </button>
            <button
              onClick={handleDownload}
              className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
            >
              <Download className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Player Controls */}
        <div className="flex flex-col items-center space-y-2 flex-1 max-w-md">
          <div className="flex items-center space-x-4">
            <button
              onClick={toggleShuffle}
              className={`p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 ${
                shuffle ? 'text-blue-600' : 'text-gray-400'
              }`}
            >
              <Shuffle className="h-4 w-4" />
            </button>
            
            <button
              onClick={previousSong}
              className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
            >
              <SkipBack className="h-5 w-5" />
            </button>
            
            <button
              onClick={togglePlayPause}
              className="p-3 bg-blue-600 hover:bg-blue-700 rounded-full text-white"
            >
              {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
            </button>
            
            <button
              onClick={nextSong}
              className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
            >
              <SkipForward className="h-5 w-5" />
            </button>
            
            <button
              onClick={() => setRepeat(repeat === 'none' ? 'all' : repeat === 'all' ? 'one' : 'none')}
              className={`p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 relative ${
                repeat !== 'none' ? 'text-blue-600' : 'text-gray-400'
              }`}
            >
              {getRepeatIcon()}
            </button>
          </div>

          {/* Progress Bar */}
          <div className="flex items-center space-x-2 w-full">
            <span className="text-xs text-gray-500 dark:text-gray-400 w-10 text-right">
              {formatTime(currentTime)}
            </span>
            <input
              type="range"
              min="0"
              max="100"
              value={duration ? (currentTime / duration) * 100 : 0}
              onChange={handleProgressChange}
              className="flex-1 progress-bar"
            />
            <span className="text-xs text-gray-500 dark:text-gray-400 w-10">
              {formatTime(duration)}
            </span>
          </div>
        </div>

        {/* Volume Control */}
        <div className="flex items-center space-x-2 flex-1 justify-end">
          <button
            onClick={toggleMute}
            className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
          >
            {isMuted || volume === 0 ? (
              <VolumeX className="h-4 w-4" />
            ) : (
              <Volume2 className="h-4 w-4" />
            )}
          </button>
          <input
            type="range"
            min="0"
            max="100"
            value={isMuted ? 0 : volume * 100}
            onChange={handleVolumeChange}
            className="volume-slider"
          />
        </div>
      </div>
    </div>
  );
};

export default MusicPlayer;
