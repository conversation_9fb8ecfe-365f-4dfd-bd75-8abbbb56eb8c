import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { Search as SearchIcon } from 'lucide-react';

const Search = () => {
  const [searchParams] = useSearchParams();
  const query = searchParams.get('q');

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="text-center py-12">
            <SearchIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Search Results
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {query ? `Searching for: "${query}"` : 'Enter a search term to find songs, artists, and playlists.'}
            </p>
            <p className="text-gray-500 dark:text-gray-400">
              Search functionality will be implemented here.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Search;
