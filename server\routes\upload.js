const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { authenticateToken, requireArtist } = require('../middleware/auth');

const router = express.Router();

// Ensure upload directories exist
const uploadDirs = ['uploads/audio', 'uploads/images', 'uploads/covers'];
uploadDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let uploadPath = 'uploads/';
    
    if (file.fieldname === 'audio') {
      uploadPath += 'audio/';
    } else if (file.fieldname === 'cover' || file.fieldname === 'profileImage') {
      uploadPath += 'images/';
    } else {
      uploadPath += 'images/';
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + extension);
  }
});

// File filter
const fileFilter = (req, file, cb) => {
  if (file.fieldname === 'audio') {
    // Accept audio files
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed for audio uploads'), false);
    }
  } else {
    // Accept image files
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed for image uploads'), false);
    }
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    files: 5 // Max 5 files per request
  }
});

// Upload audio file (artists only)
router.post('/audio', authenticateToken, requireArtist, upload.single('audio'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No audio file provided' });
    }

    const audioUrl = `/uploads/audio/${req.file.filename}`;
    
    res.json({
      message: 'Audio file uploaded successfully',
      audioUrl,
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size
    });
  } catch (error) {
    console.error('Audio upload error:', error);
    res.status(500).json({ error: 'Failed to upload audio file' });
  }
});

// Upload image file (cover art, profile images, etc.)
router.post('/image', authenticateToken, upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No image file provided' });
    }

    const imageUrl = `/uploads/images/${req.file.filename}`;
    
    res.json({
      message: 'Image uploaded successfully',
      imageUrl,
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size
    });
  } catch (error) {
    console.error('Image upload error:', error);
    res.status(500).json({ error: 'Failed to upload image file' });
  }
});

// Upload multiple files (for song with cover art)
router.post('/song-files', authenticateToken, requireArtist, upload.fields([
  { name: 'audio', maxCount: 1 },
  { name: 'cover', maxCount: 1 }
]), async (req, res) => {
  try {
    const response = {};

    if (req.files.audio && req.files.audio[0]) {
      response.audioUrl = `/uploads/audio/${req.files.audio[0].filename}`;
      response.audioFile = {
        filename: req.files.audio[0].filename,
        originalName: req.files.audio[0].originalname,
        size: req.files.audio[0].size
      };
    }

    if (req.files.cover && req.files.cover[0]) {
      response.coverArt = `/uploads/images/${req.files.cover[0].filename}`;
      response.coverFile = {
        filename: req.files.cover[0].filename,
        originalName: req.files.cover[0].originalname,
        size: req.files.cover[0].size
      };
    }

    if (!response.audioUrl && !response.coverArt) {
      return res.status(400).json({ error: 'No files provided' });
    }

    res.json({
      message: 'Files uploaded successfully',
      ...response
    });
  } catch (error) {
    console.error('Multiple file upload error:', error);
    res.status(500).json({ error: 'Failed to upload files' });
  }
});

// Delete uploaded file (cleanup)
router.delete('/file/:filename', authenticateToken, async (req, res) => {
  try {
    const { filename } = req.params;
    const { type } = req.query; // 'audio' or 'image'
    
    if (!type || !['audio', 'image'].includes(type)) {
      return res.status(400).json({ error: 'File type must be specified (audio or image)' });
    }

    const filePath = path.join(__dirname, '..', 'uploads', type === 'audio' ? 'audio' : 'images', filename);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    // Delete the file
    fs.unlinkSync(filePath);
    
    res.json({ message: 'File deleted successfully' });
  } catch (error) {
    console.error('File deletion error:', error);
    res.status(500).json({ error: 'Failed to delete file' });
  }
});

// Get file info
router.get('/info/:filename', authenticateToken, async (req, res) => {
  try {
    const { filename } = req.params;
    const { type } = req.query;
    
    if (!type || !['audio', 'image'].includes(type)) {
      return res.status(400).json({ error: 'File type must be specified (audio or image)' });
    }

    const filePath = path.join(__dirname, '..', 'uploads', type === 'audio' ? 'audio' : 'images', filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    const stats = fs.statSync(filePath);
    
    res.json({
      filename,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      url: `/uploads/${type === 'audio' ? 'audio' : 'images'}/${filename}`
    });
  } catch (error) {
    console.error('File info error:', error);
    res.status(500).json({ error: 'Failed to get file info' });
  }
});

// Error handling middleware for multer
router.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large. Maximum size is 50MB.' });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({ error: 'Too many files. Maximum is 5 files per request.' });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({ error: 'Unexpected file field.' });
    }
  }
  
  if (error.message.includes('Only')) {
    return res.status(400).json({ error: error.message });
  }
  
  next(error);
});

module.exports = router;
