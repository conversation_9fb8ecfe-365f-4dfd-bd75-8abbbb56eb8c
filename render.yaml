services:
  # PostgreSQL Database
  - type: pserv
    name: wedownstars-db
    env: node
    plan: free
    region: oregon
    
  # Backend API
  - type: web
    name: wedownstars-api
    env: node
    region: oregon
    plan: free
    buildCommand: cd server && npm install
    startCommand: cd server && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: DATABASE_URL
        fromDatabase:
          name: wedownstars-db
          property: connectionString
      - key: JWT_SECRET
        generateValue: true
      - key: PORT
        value: 10000
    
  # Frontend
  - type: web
    name: wedownstars-frontend
    env: static
    region: oregon
    plan: free
    buildCommand: cd client && npm install && npm run build
    staticPublishPath: ./client/build
    envVars:
      - key: REACT_APP_API_URL
        value: https://wedownstars-api.onrender.com/api

databases:
  - name: wedownstars-db
    databaseName: wedownstars
    user: wedownstars_user
    plan: free
    region: oregon
