import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (email, password) => api.post('/auth/login', { email, password }),
  register: (userData) => api.post('/auth/register', userData),
  getProfile: () => api.get('/auth/me'),
  updateProfile: (userData) => api.put('/auth/profile', userData),
  changePassword: (passwords) => api.put('/auth/password', passwords),
};

// Songs API
export const songsAPI = {
  getAll: (params = {}) => api.get('/songs', { params }),
  getById: (id) => api.get(`/songs/${id}`),
  create: (songData) => api.post('/songs', songData),
  update: (id, songData) => api.put(`/songs/${id}`, songData),
  delete: (id) => api.delete(`/songs/${id}`),
  like: (id) => api.post(`/songs/${id}/like`),
  recordPlay: (id, duration) => api.post(`/songs/${id}/play`, { duration }),
};

// Artists API
export const artistsAPI = {
  getAll: (params = {}) => api.get('/artists', { params }),
  getById: (id) => api.get(`/artists/${id}`),
  getSongs: (id, params = {}) => api.get(`/artists/${id}/songs`, { params }),
  update: (id, artistData) => api.put(`/artists/${id}`, artistData),
  follow: (id) => api.post(`/artists/${id}/follow`),
  getAnalytics: (id) => api.get(`/artists/${id}/analytics`),
};

// Playlists API
export const playlistsAPI = {
  getAll: (params = {}) => api.get('/playlists', { params }),
  getById: (id) => api.get(`/playlists/${id}`),
  create: (playlistData) => api.post('/playlists', playlistData),
  update: (id, playlistData) => api.put(`/playlists/${id}`, playlistData),
  delete: (id) => api.delete(`/playlists/${id}`),
  addSong: (id, songId) => api.post(`/playlists/${id}/songs`, { songId }),
  removeSong: (id, songId) => api.delete(`/playlists/${id}/songs/${songId}`),
};

// Search API
export const searchAPI = {
  search: (query, type = 'all', limit = 10) => 
    api.get('/search', { params: { q: query, type, limit } }),
};

// Upload API
export const uploadAPI = {
  uploadAudio: (file) => {
    const formData = new FormData();
    formData.append('audio', file);
    return api.post('/upload/audio', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  },
  uploadImage: (file) => {
    const formData = new FormData();
    formData.append('image', file);
    return api.post('/upload/image', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  },
  uploadSongFiles: (audioFile, coverFile) => {
    const formData = new FormData();
    if (audioFile) formData.append('audio', audioFile);
    if (coverFile) formData.append('cover', coverFile);
    return api.post('/upload/song-files', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  },
};

// Users API
export const usersAPI = {
  getLikedSongs: () => api.get('/users/likes/songs'),
  getPlaylists: () => api.get('/users/playlists'),
};

export default api;
